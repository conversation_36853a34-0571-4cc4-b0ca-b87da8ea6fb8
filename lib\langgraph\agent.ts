import { StateGraph, START, END, Annotation } from "@langchain/langgraph";
import { BaseMessage, HumanMessage, AIMessage, SystemMessage } from "@langchain/core/messages";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { z } from "zod";
import * as fs from "fs";
import * as path from "path";

// Define media item interface
export interface MediaItem {
    id: string;
    fileName: string;
    mimeType: string;
    url: string;
    filePath: string;
}

// Define the state for our agent
const AgentState = Annotation.Root({
    messages: Annotation<BaseMessage[]>({
        reducer: (x, y) => x.concat(y),
        default: () => [],
    }),
    input: Annotation<string>(),
    media: Annotation<MediaItem[]>({
        default: () => [],
    }),
    output: Annotation<string>(),
});

// Define the schema for the agent's output
const recipeSchema = z.object({
    title: z.string().min(1).max(200),
    description: z.string(),
    // Add other fields as needed
});


export class ChatAgent {
    private workflow: StateGraph<any>;
    private systemPrompt = `You are a helpful financial assistant. You can analyze financial documents, receipts, statements, and answer questions about personal finance management.`;


    }

    private mimeFromExt(filePath: string): string {
        const ext = path.extname(filePath).toLowerCase();
        switch (ext) {
            case '.jpg':
            case '.jpeg':
                return 'image/jpeg';
            case '.png':
                return 'image/png';
            case '.gif':
                return 'image/gif';
            case '.webp':
                return 'image/webp';
            case '.pdf':
                return 'application/pdf';
            case '.mp4':
                return 'video/mp4';
            case '.mov':
                return 'video/quicktime';
            case '.webm':
                return 'video/webm';
            default:
                return 'application/octet-stream';
        }
    }

    private async prepareRequestNode(state: typeof AgentState.State) {
        const { input, media } = state;
        
        // Create the content array starting with the text input
        const content: Array<{
            type: string;
            text?: string;
            mime_type?: string;
            source_type?: string;
            data?: string;
        }> = [
            {
                type: "text",
                text: input,
            },
        ];

        // Add media files to the content
        for (const mediaItem of media) {
            try {
                // Read and encode the media file as base64
                const mediaData = fs.readFileSync(mediaItem.filePath);
                const base64Media = mediaData.toString('base64');
                
                content.push({
                    type: "file",
                    mime_type: mediaItem.mimeType,
                    source_type: "base64",
                    data: base64Media,
                });

                // Add a text description for the media
                content.push({
                    type: "text",
                    text: `Attached file: ${mediaItem.fileName} (${mediaItem.mimeType})`,
                });
            } catch (error) {
                console.error(`Error reading media file ${mediaItem.fileName}:`, error);
                // Continue processing other files
            }
        }

        const humanMessage = new HumanMessage({
            content: content,
        });

        return { messages: [new SystemMessage(this.systemPrompt), humanMessage] };
    }

    private async agentNode(state: typeof AgentState.State) {
        const { messages } = state;

        // Initialize the Gemini model - using flash for multimodal support
        const model = new ChatGoogleGenerativeAI({
            model: "gemini-2.0-flash-exp", // Better model for multimodal content
            temperature: 0.1,
            maxRetries: 2,
        });

        try {
            // Invoke the model with the conversation history
            const response = await model.invoke(messages);
            return { output: response.content };
        } catch (error) {
            console.error('Model invocation error:', error);
            return { output: "I apologize, but I encountered an error processing your request. Please try again." };
        }
    }

    public async invoke(input: { messages: BaseMessage[], input: string, media?: MediaItem[] }) {

        const workflow = new StateGraph(AgentState)
            .addNode("prepareRequest", this.prepareRequestNode.bind(this))
            .addNode("agent", this.agentNode.bind(this))
            .addEdge(START, "prepareRequest")
            .addEdge("prepareRequest", "agent")
            .addEdge("agent", END);


        const agent = workflow.compile();
        const result = await agent.invoke({
            messages: input.messages || [],
            input: input.input,
            media: input.media || [],
            output: "",
        });
        return result;
    }
}
